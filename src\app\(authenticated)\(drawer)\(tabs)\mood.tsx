import ChevronLeft from '@assets/svgs/chevron-left.svg';
import ChevronRight from '@assets/svgs/chevron-right.svg';
import MoodHeart from '@assets/svgs/mood-heart.svg';
import {Button, Text, View} from '@components/native';
import {ScreenWrapper} from '@components/shared';
import {AnimatedFlashList} from '@components/shared/animated';
import {Assets} from '@constants';
import {useTheme} from '@context';
import {Slider} from '@miblanchard/react-native-slider';
import {LinearGradient} from 'expo-linear-gradient';
import {useRouter} from 'expo-router';
import React, {useState} from 'react';
import {Dimensions, Image, Pressable} from 'react-native';
import {ScrollView} from 'react-native-gesture-handler';
import Svg, {Circle, G, Line, Text as SvgText} from 'react-native-svg';

interface Mood {
    name: string;
    description: string;
    bgColor: string;
    heartColor: string;
}

const moods: Mood[] = [
    {
        name: 'Heartbroken',
        description: "This chapter is heavy,\nbut your story isn't over. 📖",
        bgColor: '#C7C3E5',
        heartColor: '#595387',
    },
    {
        name: 'Frustrated',
        description: "You're allowed to feel this way.\nLet's help you move through it. 💫",
        bgColor: '#FEDABB',
        heartColor: '#FFA04C',
    },
    {
        name: 'Anxious',
        description: "It's okay to feel this way. \nYou're not alone. 🤍",
        bgColor: '#CAD9FA',
        heartColor: '#828CFF',
    },
    {
        name: 'Optimistic',
        description: "That's wonderful! \nKeep the good vibes going! ✨",
        bgColor: '#FAF6CA',
        heartColor: '#FFFB8C',
    },
    {
        name: 'Healing',
        description: "You're learning to bloom again. \nKeep going. 🌸",
        bgColor: '#FDDEDE',
        heartColor: '#FFB7B7',
    },
];

const MoodHeartSlider: React.FC = React.memo(({}) => {
    const {colors} = useTheme();
    const [sliderValue, setSliderValue] = useState(0);
    const [selectedIndex, setSelectedIndex] = useState(0);
    const [logTrigger, setLogTrigger] = useState(0); // for MoodChart

    React.useEffect(() => {
        const rounded = Math.round(sliderValue);
        if (rounded !== selectedIndex) {
            setSelectedIndex(rounded);
        }
    }, [sliderValue, selectedIndex]);

    React.useEffect(() => {
        if (selectedIndex < 0) setSelectedIndex(0);
        if (selectedIndex > moods.length - 1) setSelectedIndex(moods.length - 1);
    }, [selectedIndex]);

    return (
        <View h={730}>
            <LinearGradient
                colors={[colors.background, moods[selectedIndex].bgColor]}
                style={{
                    position: 'absolute',
                    left: 0,
                    right: 0,
                    top: 0,
                    height: 531,
                }}
            />
            <View top={60}>
                <Text
                    fs="16"
                    fw="500"
                    color="neutral80"
                    ta="center"
                    mb={42}
                >
                    {moods[selectedIndex].description}
                </Text>
                <Text
                    fs="24"
                    fw="700"
                    color="neutral70"
                    ta="center"
                    mb={42}
                >
                    {moods[selectedIndex].name}
                </Text>
                <View
                    ai="center"
                    mb={20}
                >
                    <MoodHeart color={moods[selectedIndex].heartColor} />
                </View>
                <View px={25}>
                    <Slider
                        minimumValue={0}
                        maximumValue={moods.length - 1}
                        value={sliderValue}
                        onValueChange={(value) => setSliderValue(Array.isArray(value) ? value[0] : value)}
                        onSlidingComplete={(value) => {
                            const snapped = Math.round(value[0]);
                            setSliderValue(snapped);
                            setSelectedIndex(snapped);
                        }}
                        minimumTrackTintColor={colors.background}
                        maximumTrackTintColor={colors.background}
                        thumbTintColor={colors.purple500}
                        thumbStyle={{
                            width: 50,
                            height: 50,
                            borderRadius: 25,
                            shadowColor: '#000',
                            shadowOffset: {width: 2, height: 2},
                            shadowOpacity: 0.25,
                            shadowRadius: 5,
                            elevation: 5,
                        }}
                        trackStyle={{height: 32, borderRadius: 16}}
                    />
                </View>
                <View
                    fd="row"
                    jc="space-between"
                    mt={15}
                    pl={15}
                    pr={30}
                >
                    <Text
                        fs="10"
                        fw="400"
                        color="neutral70"
                    >
                        Heartbroken
                    </Text>
                    <Text
                        fs="10"
                        fw="400"
                        color="neutral70"
                    >
                        Frustrated
                    </Text>
                    <Text
                        fs="10"
                        fw="400"
                        color="neutral70"
                    >
                        Anxious
                    </Text>
                    <Text
                        fs="10"
                        fw="400"
                        color="neutral70"
                    >
                        Optimistic
                    </Text>
                    <Text
                        fs="10"
                        fw="400"
                        color="neutral70"
                    >
                        Healing
                    </Text>
                </View>
                <Button
                    mt={20}
                    mx={15}
                    isFullWidth={true}
                    onPress={() => setLogTrigger(Date.now())}
                >
                    LOG MOOD
                </Button>
                <MoodChart
                    selectedMood={moods[selectedIndex]}
                    triggerAddDot={logTrigger}
                />
            </View>
        </View>
    );
});

interface MoodChartProps {
    selectedMood: Mood;
    triggerAddDot: number;
}

interface MoodData {
    [week: string]: {day: number; hour: number; color: string}[];
}

const {push} = useRouter();

const MoodChart: React.FC<MoodChartProps> = ({selectedMood, triggerAddDot}) => {
    const getStartOfWeek = (date: Date) => {
        const d = new Date(date);
        const day = d.getDay();
        const diff = d.getDate() - day;
        return new Date(d.setDate(diff));
    };

    const getEndOfWeek = (date: Date) => {
        const start = getStartOfWeek(date);
        return new Date(start.getFullYear(), start.getMonth(), start.getDate() + 6);
    };

    const formatWeekRange = (date: Date) => {
        const start = getStartOfWeek(date);
        const end = getEndOfWeek(date);
        const options: Intl.DateTimeFormatOptions = {day: '2-digit', month: 'short'};
        return `${start.getDate()} ${start.toLocaleString('default', {month: 'short'})} - ${end.getDate()} ${end.toLocaleString('default', {month: 'short'})}`;
    };

    // Mock mood data: { weekStart: string, dots: [{ day: 0-6, hour: 0-23, color: string }] }
    const useMoodChartData = () => {
        const [moodData, setMoodData] = React.useState<MoodData>({});
        const addMood = (weekKey: string, day: number, hour: number, color: string, actualDate: Date) => {
            setMoodData((prev) => {
                const week = prev[weekKey] || [];
                console.log(`Mood logged for week [${weekKey}] on [${actualDate.toDateString()}]: day=${day}, hour=${hour}, color=${color}`);
                return {...prev, [weekKey]: [...week, {day, hour, color}]};
            });
        };
        return {moodData, addMood};
    };

    const {colors} = useTheme();
    const [currentWeek, setCurrentWeek] = React.useState(getStartOfWeek(new Date()));
    const {moodData, addMood} = useMoodChartData();
    const [lastTrigger, setLastTrigger] = React.useState(0);

    React.useEffect(() => {
        if (triggerAddDot && triggerAddDot !== lastTrigger) {
            const now = new Date();
            const weekKey = getStartOfWeek(now).toDateString();
            addMood(weekKey, now.getDay(), now.getHours(), selectedMood.heartColor, now);
            setLastTrigger(triggerAddDot);
        }
    }, [triggerAddDot, selectedMood, addMood, lastTrigger]);

    const weekKey = getStartOfWeek(currentWeek).toDateString();
    const dots = moodData[weekKey] || [];
    const yTimes = ['12 am', '4 am', '8 am', '12 pm', '4 pm', '8 pm', '11 pm'];
    const weekDays = [
        {label: 'S', id: 0},
        {label: 'M', id: 1},
        {label: 'T', id: 2},
        {label: 'W', id: 3},
        {label: 'T', id: 4},
        {label: 'F', id: 5},
        {label: 'S', id: 6},
    ];

    // Chart dimensions
    const width = Dimensions.get('window').width;
    const height = 309;
    const padding = 45;
    const chartW = width - padding * 2;
    const chartH = height - padding * 2;
    const dayStep = chartW / 6;
    const timeStep = chartH / 6;

    const getY = (hour: number) => {
        const pointSpacing = chartH / 23;
        const roundedHour = Math.round(hour);
        return padding + roundedHour * pointSpacing;
    };

    return (
        <View
            mt={30}
            mb={20}
            px={10}
        >
            {/* Week Selector */}
            <View
                fd="row"
                ai="center"
                jc="space-between"
            >
                <ChevronLeft onPress={() => setCurrentWeek((prev) => new Date(prev.getFullYear(), prev.getMonth(), prev.getDate() - 7))} />
                <Text
                    fs="16"
                    fw="600"
                    color="neutral80"
                >
                    {formatWeekRange(currentWeek)}
                </Text>
                <ChevronRight
                    onPress={() => {
                        const next = new Date(currentWeek.getFullYear(), currentWeek.getMonth(), currentWeek.getDate() + 7);
                        if (next <= getStartOfWeek(new Date())) setCurrentWeek(next);
                    }}
                />
            </View>
            {/* Chart */}
            <Svg
                width={width}
                height={height}
            >
                {/* Y axis labels */}
                {yTimes.map((label, i) => (
                    <SvgText
                        key={label}
                        x={padding - 8}
                        y={padding + i * timeStep + 4}
                        fontFamily="MontserratRegular"
                        fontSize="12"
                        fontWeight={'400'}
                        fill={colors.neutral50}
                        textAnchor="end"
                    >
                        {label}
                    </SvgText>
                ))}
                {/* Vertical dotted lines */}
                {weekDays.map((d, i) => (
                    <G key={d.id}>
                        <Line
                            x1={padding + i * dayStep}
                            y1={padding}
                            x2={padding + i * dayStep}
                            y2={height - padding}
                            stroke={colors.neutral10}
                            strokeDasharray="8 8"
                            strokeWidth={1}
                        />
                        <SvgText
                            x={padding + i * dayStep}
                            y={height - padding + 16}
                            fontFamily="MontserratRegular"
                            fontSize="10"
                            fill={colors.neutral60}
                            textAnchor="middle"
                        >
                            {d.label}
                        </SvgText>
                    </G>
                ))}
                {/* Mood dots */}
                {dots.map((dot, idx) => (
                    <Circle
                        key={idx}
                        cx={padding + dot.day * dayStep}
                        cy={getY(dot.hour)}
                        r={6}
                        fill={dot.color}
                        stroke="#fff"
                        strokeWidth={2}
                    />
                ))}
            </Svg>
            <View
                pos="absolute"
                right={0.01}
                bottom={0.01}
                style={{width: '40%', height: '85%', opacity: 0.7, backgroundColor: colors.background}}
            />
            <View
                pos="absolute"
                right={11}
                top={'50%'}
                style={{
                    transform: [{translateY: -20}],
                }}
            >
                <Button
                    bg="purple100"
                    isFullWidth={false}
                    onPress={() =>
                        push({
                            pathname: '/(authenticated)/(screens)/summary',
                            params: {
                                moodData: JSON.stringify(moodData),
                            },
                        })
                    }
                >
                    <View
                        fd="row"
                        ai="center"
                        gap={2}
                    >
                        <Text
                            fs="14"
                            fw="600"
                            color="purple500"
                        >
                            Summary
                        </Text>
                        <ChevronRight color={colors.purple500} />
                    </View>
                </Button>
            </View>
        </View>
    );
};

const MoodTabScreen = () => {
    return (
        <ScreenWrapper>
            <ScrollView>
                <MoodHeartSlider />
                <Text
                    fs="14"
                    fw="600"
                    color="neutral80"
                    ml={16}
                    mb={16}
                >
                    Based on your mood lately
                </Text>
                <AnimatedFlashList
                    horizontal
                    disabledSafeArea
                    data={[1, 2, 3, 4, 5]}
                    renderItem={({item}: {item: number}) => (
                        <Pressable
                            onPress={() =>
                                push({
                                    pathname: '/(authenticated)/(screens)/article',
                                    params: {
                                        title: 'Embracing Every Emotion: Why Tracking Your Mood Matters',
                                        image: Assets.placeholder.article,
                                        content:
                                            "Embracing every emotion is a vital part of personal growth and mental well-being. By tracking your moods, you gain valuable insights into your emotional patterns, triggers, and progress over time. This awareness helps you better understand yourself, manage stress, and celebrate your healing journey.\n\nMood tracking isn't just about noticing the tough days—it's about recognizing your resilience and the moments of joy, too. Whether you're feeling heartbroken, anxious, or optimistic, every emotion is valid and worth acknowledging. Start your mood journal today and take the first step toward a more mindful, empowered you.",
                                    },
                                })
                            }
                        >
                            <View
                                w={122}
                                h={141}
                                br={12}
                                bg="background"
                                ml={16}
                                bw={1}
                                bc="neutral10"
                            >
                                <Image
                                    source={Assets.placeholder.article}
                                    height={97}
                                    style={{
                                        height: 120,
                                        width: '100%',
                                        borderTopLeftRadius: 12,
                                        borderTopRightRadius: 12,
                                    }}
                                />
                                <Text
                                    fs="10"
                                    fw="500"
                                    color="neutral80"
                                    mx={9}
                                    my={8}
                                    numberOfLines={3}
                                >
                                    Embracing Every Emotion: Why Tracking Your Mood Matters
                                </Text>
                            </View>
                        </Pressable>
                    )}
                    estimatedItemSize={5}
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={{paddingRight: 16, paddingBottom: 30}}
                />
            </ScrollView>
        </ScreenWrapper>
    );
};

export default MoodTabScreen;
