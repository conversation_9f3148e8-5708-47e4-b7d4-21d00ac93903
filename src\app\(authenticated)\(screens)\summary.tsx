import AnxiousDay from '@assets/svgs/anxious-day.svg';
import FrustratedDay from '@assets/svgs/frustrated-day.svg';
import HealingDay from '@assets/svgs/healing-day.svg';
import HeartbrokenDay from '@assets/svgs/heartbroken-day.svg';
import OptimisticDay from '@assets/svgs/optimistic-day.svg';
import {Text, View} from '@components/native';
import {FontFamilyMap} from '@components/native/Text';
import {ScreenWrapper, StackBackButton} from '@components/shared';
import {useTheme} from '@context';
import {useLocalSearchParams} from 'expo-router';
import React, {useMemo} from 'react';
import {CalendarList} from 'react-native-calendars';

interface MoodData {
    [week: string]: {day: number; hour: number; color: string}[];
}

const SummaryScreen = () => {
    const params = useLocalSearchParams();
    const {colors} = useTheme();

    // Parse moodData from params
    const moodData: MoodData = useMemo(() => {
        try {
            return params.moodData ? JSON.parse(params.moodData as string) : {};
        } catch (error) {
            console.error('Error parsing moodData:', error);
            return {};
        }
    }, [params.moodData]);

    // Helper function to convert week key to date
    const weekKeyToDate = (weekKey: string) => {
        return new Date(weekKey);
    };

    // Helper function to format date as YYYY-MM-DD
    const formatDateString = (date: Date) => {
        return date.toISOString().split('T')[0];
    };

    // Calculate date range and marked dates
    const {markedDates} = useMemo(() => {
        const currentDate = new Date();
        const currentMonth = formatDateString(new Date(currentDate.getFullYear(), currentDate.getMonth(), 1));

        // If no mood data, show only current month
        if (!moodData || Object.keys(moodData).length === 0) {
            return {
                minDate: currentMonth,
                maxDate: currentMonth,
                markedDates: {},
            };
        }

        // Create marked dates object
        const marked: {[key: string]: any} = {};

        // Process each week's mood data
        Object.entries(moodData).forEach(([weekKey, moods]) => {
            if (moods && moods.length > 0) {
                // Get the week start date
                const weekStartDate = weekKeyToDate(weekKey);

                // Mark each day of the week that has mood data
                moods.forEach((mood) => {
                    const moodDate = new Date(weekStartDate);
                    moodDate.setDate(moodDate.getDate() + mood.day + 1);
                    const dateString = formatDateString(moodDate);

                    marked[dateString] = {
                        customStyles: {
                            container: {
                                backgroundColor: mood.color,
                                borderRadius: 16,
                            },
                        },
                    };
                });
            }
        });

        return {
            markedDates: marked,
        };
    }, [moodData]);

    return (
        <ScreenWrapper>
            <View p={16}>
                <StackBackButton title="Mood Summary" />
            </View>
            <View
                fd="row"
                jc="space-evenly"
                ai="center"
            >
                <Text
                    color="neutral80"
                    fw="400"
                    fs="10"
                >
                    S
                </Text>
                <Text
                    color="neutral80"
                    fw="400"
                    fs="10"
                >
                    M
                </Text>
                <Text
                    color="neutral80"
                    fw="400"
                    fs="10"
                >
                    T
                </Text>
                <Text
                    color="neutral80"
                    fw="400"
                    fs="10"
                >
                    W
                </Text>
                <Text
                    color="neutral80"
                    fw="400"
                    fs="10"
                >
                    T
                </Text>
                <Text
                    color="neutral80"
                    fw="400"
                    fs="10"
                >
                    F
                </Text>
                <Text
                    color="neutral80"
                    fw="400"
                    fs="10"
                >
                    S
                </Text>
            </View>
            <View
                h={1}
                bg="neutral20"
                mt={10}
            />
            <View flex={1}>
                <CalendarList
                    // Marking configuration
                    markingType="custom"
                    markedDates={markedDates}
                    // dayComponent={DayComponent}
                    // Disable interactions (read-only)
                    disabledByDefault={true}
                    disableAllTouchEventsForDisabledDays={true}
                    disableAllTouchEventsForInactiveDays={true}
                    onDayPress={undefined}
                    onDayLongPress={undefined}
                    pastScrollRange={(() => {
                        const weekKeys = Object.keys(moodData);
                        if (!moodData || weekKeys.length === 0) return 0;
                        const firstMoodDate = weekKeyToDate(weekKeys[0]);
                        const currentDate = new Date();
                        const monthDiff = (currentDate.getFullYear() - firstMoodDate.getFullYear()) * 12 + (currentDate.getMonth() - firstMoodDate.getMonth()) - 1;
                        return Math.max(0, monthDiff);
                    })()}
                    futureScrollRange={0}
                    showScrollIndicator={false}
                    // Styling
                    theme={{
                        backgroundColor: colors.background,
                        calendarBackground: colors.background,
                        textDisabledColor: colors.neutral80,
                        monthTextColor: colors.neutral80,
                        todayTextColor: colors.neutral80,
                        todayBackgroundColor: colors.purpleLight,
                        todayButtonFontFamily: FontFamilyMap['600'],
                        todayButtonFontSize: 16,
                        textMonthFontFamily: FontFamilyMap['700'],
                        textMonthFontSize: 16,
                        textDayFontFamily: FontFamilyMap['400'],
                        textDayFontSize: 16,
                    }}
                    // Calendar styling
                    calendarStyle={{}}
                    // Hide arrows for read-only experience
                    hideArrows={true}
                    disableMonthChange={true}
                    hideExtraDays={true}
                    hideDayNames={true}
                />
            </View>
        </ScreenWrapper>
    );
};

export default SummaryScreen;
